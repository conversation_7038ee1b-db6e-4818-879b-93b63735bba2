package config

import (
    "bufio"
    "fmt"
    "os"
    "strings"
)

// Config holds the application configuration
type Config struct {
    JiraAPIToken string
    TestPlanKey  string
}

// GlobalConfig is the global configuration instance
var GlobalConfig *Config

// LoadConfig loads configuration from .env file and environment variables
func LoadConfig() error {
    config := &Config{}
    
    // Try to load from .env file first
    err := loadFromEnvFile(config, ".env")
    if err != nil {
        fmt.Printf("Warning: Could not load .env file: %v\n", err)
    }
    
    // Override with actual environment variables if they exist
    if token := os.Getenv("JIRA_API_TOKEN"); token != "" {
        config.JiraAPIToken = token
    }
    if testPlan := os.Getenv("TEST_PLAN_KEY"); testPlan != "" {
        config.TestPlanKey = testPlan
    }

    // Validate required configuration
    if config.JiraAPIToken == "" {
        return fmt.Errorf("JIRA_API_TOKEN is required but not set")
    }
    if config.TestPlanKey == "" {
        return fmt.Errorf("TEST_PLAN_KEY is required but not set")
    }
    
    GlobalConfig = config
    return nil
}

// loadFromEnvFile loads configuration from a .env file
func loadFromEnvFile(config *Config, filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer file.Close()
    
    scanner := bufio.NewScanner(file)
    for scanner.Scan() {
        line := strings.TrimSpace(scanner.Text())
        
        // Skip empty lines and comments
        if line == "" || strings.HasPrefix(line, "#") {
            continue
        }
        
        // Parse key=value or key = "value" format
        parts := strings.SplitN(line, "=", 2)
        if len(parts) != 2 {
            continue
        }
        
        key := strings.TrimSpace(parts[0])
        value := strings.TrimSpace(parts[1])
        
        // Remove quotes if present
        if len(value) >= 2 && ((value[0] == '"' && value[len(value)-1] == '"') || 
            (value[0] == '\'' && value[len(value)-1] == '\'')) {
            value = value[1 : len(value)-1]
        }
        
        // Set configuration values based on key
        switch key {
        case "JiraAPIKey", "JIRA_API_TOKEN":
            config.JiraAPIToken = value
        case "TestPlanKey", "TEST_PLAN_KEY":
            config.TestPlanKey = value
        }
    }
    
    return scanner.Err()
}

// GetJiraAPIToken returns the Jira API token
func GetJiraAPIToken() string {
    if GlobalConfig == nil {
        return ""
    }
    return GlobalConfig.JiraAPIToken
}

// GetTestPlanKey returns the test plan key
func GetTestPlanKey() string {
    if GlobalConfig == nil {
        return ""
    }
    return GlobalConfig.TestPlanKey
}

func SetTestPlanKey(newKey string) {
    GlobalConfig.TestPlanKey = newKey

    // edit the .env file
    file, err := os.OpenFile(".env", os.O_RDWR, 0644)
    if err != nil {
        fmt.Printf("Error opening .env file: %v\n", err)
        return
    }
    defer file.Close()

    // Read the file line by line
    var lines []string
    scanner := bufio.NewScanner(file)
    for scanner.Scan() {
        line := scanner.Text()
        if strings.HasPrefix(line, "TEST_PLAN_KEY=") {
            line = "TEST_PLAN_KEY=" + newKey
        }
        lines = append(lines, line)
    }

    if err := scanner.Err(); err != nil {
		fmt.Println("Error reading file:", err)
		return
    }
    

    // Write the modified lines back to the file
    file.Seek(0, 0)
    file.Truncate(0)
    writer := bufio.NewWriter(file)
	for _, line := range lines {
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			fmt.Println("Error writing to file:", err)
			return
		}
	}
	writer.Flush()
}
