package gui

import (
    "fyne.io/fyne/v2"
    "fyne.io/fyne/v2/app"
    "fyne.io/fyne/v2/container"
    "fyne.io/fyne/v2/widget"

    "fmt"
)

var application fyne.App
var window fyne.Window

// singleton window
func getWindow() fyne.Window {
    if application == nil {
        application = app.New()
    }
    if window == nil {
        window = application.NewWindow("Jira Test Plan Exporter")
        // window.Show()
        // window.Hide()
    }
    return window
}

// pops up a window with a textbox (with a default value) and a button to close. Returns textbox input
func Input(defaultKey string) string {
    window := getWindow()

    entry := widget.NewEntry()
    entry.Text = defaultKey
    entry.SetPlaceHolder("Enter Test Plan Key")
    entry.OnSubmitted = func(string) {
        fmt.Println("Using test plan key:", entry.Text)
        window.Hide()
    }

    button := widget.NewButton("Submit", func() {
        fmt.Println("Using test plan key:", entry.Text)
        window.Hide()
    })

    window.SetContent(container.NewVBox(entry, button))
    window.Resize(fyne.NewSize(240, 100))
    window.CenterOnScreen()
    window.Show()

    return entry.Text
}

// pops up a window with a message and a button to close. Returns true if the button was pressed
func Alert(message string, buttonText string) bool {
    buttonPressed := false
    window := getWindow()
    button := widget.NewButton(buttonText, func() {
        buttonPressed = true
        window.Hide()
    })
    closeButton := widget.NewButton("Close", func() {
        window.Hide()
    })
    window.SetContent(container.NewVBox(widget.NewLabel(message), container.NewGridWithColumns(2, button,closeButton)))
    window.Resize(fyne.NewSize(240, 100))
    window.CenterOnScreen()
    window.Show()

    return buttonPressed
}
