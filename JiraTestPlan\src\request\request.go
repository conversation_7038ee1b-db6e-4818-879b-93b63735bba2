package request

import (
    "fmt"
    "io"
    "net/http"
    "net/url"
    "sync"
    "time"
)

var (
    // Shared HTTP client with optimized settings
    httpClient *http.Client
    clientOnce sync.Once
)

// getOptimizedClient returns a singleton HTTP client with optimized settings
func getOptimizedClient() *http.Client {
    clientOnce.Do(func() {
        transport := &http.Transport{
            MaxIdleConns:        100,
            MaxIdleConnsPerHost: 10,
            IdleConnTimeout:     90 * time.Second,
            DisableCompression:  false,
        }

        httpClient = &http.Client{
            Transport: transport,
            Timeout:   30 * time.Second,
        }
    })
    return httpClient
}

// MakeRequest returns response data directly as bytes for better performance
func MakeRequest(bearerToken string, endpoint string, queryParameters map[string]string, writer io.Writer) error {
    jiraBaseURL := "https://jerry.dieboldnixdorf.com/rest/"

    // Parse the URL and add query parameters
    parsedURL, parseError := url.Parse(jiraBaseURL + endpoint)
    if parseError != nil {
        return fmt.Errorf("error parsing URL: %w", parseError)
    }

    // Add query parameters
    urlQuery := parsedURL.Query()
    for parameterKey, parameterValue := range queryParameters {
        urlQuery.Add(parameterKey, parameterValue)
    }
    parsedURL.RawQuery = urlQuery.Encode()

    // Create a new request
    req, err := http.NewRequest("GET", parsedURL.String(), nil)
    if err != nil {
        return fmt.Errorf("error creating request: %w", err)
    }

    // Add the Bearer token to the Authorization header
    req.Header.Add("Authorization", "Bearer " + bearerToken)

    // Use the optimized client
    client := getOptimizedClient()
    resp, err := client.Do(req)
    if err != nil {
        return fmt.Errorf("error making request: %w", err)
    }
    if resp.StatusCode == http.StatusTooManyRequests {
        fmt.Printf("Too many requests, waiting 1 second...\n")
        time.Sleep(1 * time.Second)
        return MakeRequest(bearerToken, endpoint, queryParameters, writer)
    }
    defer resp.Body.Close()

    // Write output to writer
    _, err = io.Copy(writer, resp.Body)
    return err
}
