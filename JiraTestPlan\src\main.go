package main

import (
	"JiraTestPlan/config"
	"JiraTestPlan/request"
	"JiraTestPlan/gui"
	"bytes"
	"encoding/json"
	"fmt"
	"sync"
	"time"
    // "strings"

	"github.com/xuri/excelize/v2"    
    // "github.com/atotto/clipboard"
)

// Global API token - loaded from config
var apiToken string

// JiraResponse represents the structure of Jira API response
type JiraResponse struct {
    Issues []struct {
        Key    string `json:"key"`
        Fields struct {
            Summary        string `json:"summary"`
            Timestamp      string `json:"timestamp"`
            Status         struct {
                Name string `json:"name"`
            } `json:"status"`
            Statuses []struct {
                StatusResults []struct {
                    Latest       int `json:"latest"`
                    LatestFinal  int `json:"latestFinal"`
                } `json:"statusResults"`
            } `json:"statuses"`
        } `json:"fields"`
    } `json:"issues"`
}

// JiraTest holds detailed information about a test
type JiraTest struct {
    Key        string
    Summary    string
    Execution  string
    Status     string `json:"status"`
    FinishedOn string `json:"finishedOn"`
}

// TestExecution holds the result of getting tests for an execution
type TestExecution struct {
    Execution string
    Tests     []JiraTest
    Error     error
}

// given a test plan, get all the test executions
func getExecutions(testPlan string) ([]string, error) {
    endpoint := "api/2/search"

    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + testPlan + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key,summary",
    }
    var responseData bytes.Buffer
    err := request.MakeRequest(apiToken, endpoint, params, &responseData)
    if err != nil {
        return nil, fmt.Errorf("error making request: %w", err)
    }

    var jsonData JiraResponse
    err = json.Unmarshal(responseData.Bytes(), &jsonData)
    if err != nil {
        return nil, fmt.Errorf("error parsing JSON: %w", err)
    }

    keys := make([]string, 0, len(jsonData.Issues))
    for _, issue := range jsonData.Issues {
        keys = append(keys, issue.Key)
    }
    return keys, nil
}

// get all the tests in the test execution
func getTests(testExecution string) ([]JiraTest, error) {
    endpoint := "api/2/search"

    params := map[string]string{
        "jql":        "issue in testExecutionTests('" + testExecution + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key,summary,resolutiondate,status",
    }
    
    var responseData bytes.Buffer
    err := request.MakeRequest(apiToken, endpoint, params, &responseData)
    if err != nil {
        return nil, fmt.Errorf("error making request: %w", err)
    }

    var jsonData JiraResponse
    err = json.Unmarshal(responseData.Bytes(), &jsonData)
    if err != nil {
        return nil, fmt.Errorf("error parsing JSON: %w", err)
    }

    tests := make([]JiraTest, 0, len(jsonData.Issues))
    for _, issue := range jsonData.Issues {
        testInfo := JiraTest{
            Key:        issue.Key,
            Summary:    issue.Fields.Summary,
        }
        tests = append(tests, testInfo)
    }
    return tests, nil
}

// get the test status from Xray (is it PASS, TODO, FAIL, etc.)
func getStatus(testExecKey string, testKey string) (JiraTest, error) {
    endpoint := "raven/2.0/api/testrun"

    params := map[string]string{
        "testExecIssueKey": testExecKey,
        "testIssueKey":     testKey,
    }

    var responseData bytes.Buffer
    err := request.MakeRequest(apiToken, endpoint, params, &responseData)
    if err != nil {
        return JiraTest{}, fmt.Errorf("error making request: %w", err)
    }


    var testRunInfo JiraTest
    err = json.Unmarshal(responseData.Bytes(), &testRunInfo)
    if err != nil {
        return JiraTest{}, fmt.Errorf("error parsing JSON: %w", err)
    }

    return testRunInfo, nil
}

// getTestsWithStatus gets tests and their status for an execution
func getTestsWithStatus(testExecution string) ([]JiraTest, error) {
    // First get the basic test information
    tests, err := getTests(testExecution)
    if err != nil {
        return nil, err
    }

    // Then get the run status for each test
    enhancedTests := make([]JiraTest, 0, len(tests))

    // limit to 20 threads running at once
    statusSemaphore := make(chan struct{}, 5)
    var wg sync.WaitGroup 
    var mutex sync.Mutex

    for _, test := range tests {
        wg.Add(1)
        go func(test JiraTest) {
            defer wg.Done()
            statusSemaphore <- struct{}{}
            defer func() { <-statusSemaphore }()

            runInfo, err := getStatus(testExecution, test.Key)
            if err != nil {
                // If we can't get run status, still include the test with basic info
                fmt.Printf("Warning: Could not get run status for test %s: %v\n", test.Key, err)
                enhancedTest := JiraTest{
                    Key:           test.Key,
                    Summary:       test.Summary,
                    FinishedOn:    test.FinishedOn,
                    Status:        test.Status,
                }
                mutex.Lock()
                enhancedTests = append(enhancedTests, enhancedTest)
                mutex.Unlock()
                } else {
                    enhancedTest := JiraTest{
                        Key:           test.Key,
                        Summary:       test.Summary,
                        FinishedOn:    runInfo.FinishedOn,
                        Status:        runInfo.Status,
                    }
                mutex.Lock()
                enhancedTests = append(enhancedTests, enhancedTest)
                mutex.Unlock()
            }
        } (test)
    }
    wg.Wait()
    return enhancedTests, nil
}

// getTests fetches tests for all executions concurrently
func getTestsConcurrently(executions []string, maxConcurrency int) []TestExecution {
    results := make([]TestExecution, len(executions))

    // Create a semaphore to limit concurrent requests
    semaphore := make(chan struct{}, maxConcurrency)
    var wg sync.WaitGroup

    for i, execution := range executions {
        wg.Add(1)
        go func(index int, exec string) {
            defer wg.Done()

            // Acquire semaphore
            semaphore <- struct{}{}
            defer func() { <-semaphore }()

            tests, err := getTestsWithStatus(exec)
            results[index] = TestExecution{
                Execution: exec,
                Tests:     tests,
                Error:     err,
            }

            fmt.Printf("Found %d tests for execution %s\n", len(results[index].Tests), results[index].Execution)
        }(i, execution)
    }

    wg.Wait()
    return results
}

// formatDate extracts just the date part from ISO datetime string
func formatDate(dateTimeStr string) string {
    if dateTimeStr == "" {
        return ""
    }

    // Parse the ISO datetime string
    parsedTime, err := time.Parse(time.RFC3339, dateTimeStr)
    if err != nil {
        // If parsing fails, try to extract just the date part manually
        if len(dateTimeStr) >= 10 {
            return dateTimeStr[:10] // Extract YYYY-MM-DD
        }
        return dateTimeStr // Return original if we can't parse it
    }

    // Format as YYYY-MM-DD
    return parsedTime.Format("2006-01-02")
}

// exportToXLSX exports test results to an XLSX file with custom formatting
func exportToXLSX(results *map[string]JiraTest, filename string) error {
    f := excelize.NewFile()
    defer func() {
        if err := f.Close(); err != nil {
            fmt.Printf("Error closing Excel file: %v\n", err)
        }
    }()

    // Get the default sheet name
    sheetName := f.GetSheetName(0)

    // Write headers on row 6
    headers := []string{"Key", "Summary", "Test Category", "Labels", "Priority", "Assignee", "Last test status", "Finished On"}
    for i, header := range headers {
        cell := fmt.Sprintf("%c1", 'A'+i)
        f.SetCellValue(sheetName, cell, header)
    }

    // Start writing data on row 8, skipping every odd row (8, 10, 12, etc.)
    currentRow := 2
    for key, test := range *results {
        rowData := []interface{}{
            key,                    // Key
            test.Summary,                // Summary
            "",                          // Test Category (blank)
            "",                          // Labels (blank)
            "",                          // Priority (blank)
            "",                          // Assignee (blank)
            test.Status,                 // Last test run status
            formatDate(test.FinishedOn), // Finished On (date only)
        }
        for i, value := range rowData {
            cell := fmt.Sprintf("%c%d", 'A'+i, currentRow)
            f.SetCellValue(sheetName, cell, value)
        }
        currentRow += 1 
        
    }

    // style
    f.SetColWidth(sheetName, "A", "H", 13)
    f.SetColWidth(sheetName, "B", "B", 50)
	// Define a style with bold text and a specific font color
	style, err := f.NewStyle(&excelize.Style{
        Font: &excelize.Font{
			Bold: true,
		},
        Fill: excelize.Fill{
			Type: "pattern",
            Pattern: 1,
            Color: []string{ "#b8dff5"},
        },
    },
    )
    if err != nil {
        panic(err)
    }
    f.SetCellStyle(sheetName, "A1", "H1", style)

    // Save the file
    if err := f.SaveAs(filename); err != nil {
        return fmt.Errorf("error saving XLSX file: %w", err)
    }

    return nil
}

// return the jira test with a newer finished on date
// except that having no finished on date counts as newer
func newer(first JiraTest, second JiraTest) JiraTest {
    if first.FinishedOn == "" {
        return first
    }
    if second.FinishedOn == "" {
        return second
    }

    if first.FinishedOn > second.FinishedOn {
        return first
    }
    return second 
}

func main() {
    response := gui.Alert("bruh", "Copy")
    fmt.Println(response)
    response = gui.Alert("bruh", "Copy")
    fmt.Println(response)
    response = gui.Alert("bruh", "Copy")
    fmt.Println(response)
    return

    // Load configuration from .env file
    err := config.LoadConfig()
    if err != nil {
        fmt.Printf("Error loading configuration: %v\n", err)
        return
    }

    // Set the global API token and get test plan key
    apiToken = config.GetJiraAPIToken()
    testPlanKey := config.GetTestPlanKey()
    
    // use gui
    input := gui.Input(testPlanKey)
    config.SetTestPlanKey(input)
    testPlanKey = config.GetTestPlanKey()
    
    fmt.Printf("Loaded API token: %s...\n", apiToken[:10]) // Show first 10 chars for verification
    fmt.Printf("Using test plan key: %s\n", testPlanKey)

    // Start timing
    startTime := time.Now()

    fmt.Println("Fetching executions...")
    executions, err := getExecutions(testPlanKey)
    if err != nil {
        fmt.Printf("Error getting executions: %v\n", err)
        return
    }
    fmt.Printf("Found %d executions\n", len(executions))

    if len(executions) == 0 {
        fmt.Println("No executions found")
        return
    }

    // Fetch tests concurrently with a reasonable limit
    maxConcurrency := 30 // Adjust based on API rate limits
    fmt.Printf("Fetching tests for all executions concurrently (max %d concurrent requests)...\n", maxConcurrency)

    results := getTestsConcurrently(executions, maxConcurrency)

    uniqueTests := make(map[string]JiraTest)
    duplicatedTests := make([]string, 0, len(results)/2)
    for _, execution := range results {
        for _, currentTest := range execution.Tests {
            savedTest, found := uniqueTests[currentTest.Key]
            if !found {
                uniqueTests[currentTest.Key] = currentTest
                continue
            }

            duplicatedTests = append(duplicatedTests, currentTest.Key)

            // keep the newer test
            uniqueTests[currentTest.Key] = newer(currentTest, savedTest)
        }
    }
    // Warning: Found duplicated tests \n Note, the export try to continue. \n\n 
    // response := gui.Alert("bruh", "Copy")
    // if response {
    //     clipboard.WriteAll(strings.Join(duplicatedTests, ", "))
    // }

    elapsed := time.Since(startTime)
    fmt.Printf("\nTotal: %d tests across %d executions\n", len(uniqueTests), len(executions))
    fmt.Printf("Completed in: %v\n", elapsed)

    // Export to XLSX
    xlsxFilename := fmt.Sprintf("%s %s.xlsx", testPlanKey, time.Now().Format("2006_01_02_15_04"))
    fmt.Printf("\nExporting results to %s...\n", xlsxFilename)
    err = exportToXLSX(&uniqueTests, xlsxFilename)
    if err != nil {
        fmt.Printf("Error exporting to XLSX: %v\n", err)
        return
    }
    fmt.Printf("Successfully exported %d tests to %s\n", len(uniqueTests), xlsxFilename)
}
